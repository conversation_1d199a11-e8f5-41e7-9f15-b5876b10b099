<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />
    <MainContent>
      <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">批量合同分析</h1>
          <p class="text-gray-600">
            同时上传多个合同文件进行批量分析，提高工作效率
          </p>
        </div>

        <!-- 批量上传区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">文件上传</h2>
          <BatchAnalysisUpload />
        </div>

        <!-- 分析设置 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">分析设置</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 分析类型 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">分析类型</label>
              <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="full">全面分析</option>
                <option value="risk">风险分析</option>
                <option value="clause">条款分析</option>
              </select>
            </div>

            <!-- 优先级 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">处理优先级</label>
              <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="normal">普通</option>
                <option value="high">高</option>
                <option value="urgent">紧急</option>
              </select>
            </div>
          </div>

          <!-- 通知设置 -->
          <div class="mt-6">
            <label class="flex items-center">
              <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
              <span class="ml-2 text-sm text-gray-700">分析完成后发送邮件通知</span>
            </label>
          </div>
        </div>

        <!-- 功能说明 -->
        <div class="bg-blue-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-blue-900 mb-3">批量分析功能说明</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-blue-900">并行处理</h4>
                <p class="text-sm text-blue-700">多个文件同时进行分析，大幅提升处理效率</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-blue-900">统一报告</h4>
                <p class="text-sm text-blue-700">生成综合分析报告，便于对比和管理</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-blue-900">进度跟踪</h4>
                <p class="text-sm text-blue-700">实时查看每个文件的分析进度和状态</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-blue-900">智能分类</h4>
                <p class="text-sm text-blue-700">自动识别合同类型并应用相应的分析模板</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">注意事项</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>批量分析功能目前正在开发中，敬请期待</li>
                  <li>建议单次上传文件数量不超过10个</li>
                  <li>大文件处理可能需要较长时间，请耐心等待</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainContent>
    <AppFooter />
  </div>
</template>

<script setup>
import AppFooter from './layout/AppFooter.vue'
import AppHeader from './layout/AppHeader.vue'
import MainContent from './layout/MainContent.vue'
import BatchAnalysisUpload from './analysis/BatchAnalysisUpload.vue'
</script>

<style>
/* 可以添加额外的样式，但大部分样式已通过Tailwind类实现 */
</style>
