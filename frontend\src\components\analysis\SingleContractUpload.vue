<template>
  <div>
    <!-- 文件上传区域 -->
    <div 
      @drop="handleFileDrop"
      @dragover.prevent
      @dragenter.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      :class="[
        'border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer',
        isDragging 
          ? 'border-blue-500 bg-blue-50' 
          : selectedFile 
            ? 'border-green-500 bg-green-50' 
            : 'border-gray-300 hover:border-gray-400'
      ]"
      @click="$refs.fileInput.click()"
    >
      <!-- 上传图标 -->
      <div class="flex justify-center mb-4">
        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
        </div>
      </div>

      <!-- 上传文本 -->
      <div v-if="!selectedFile">
        <h3 class="text-lg font-medium text-gray-900 mb-2">上传合同文件</h3>
        <p class="text-sm text-gray-500 mb-4">
          拖拽文件到此处或点击上传，支持PDF格式，最大20MB
        </p>
        <button 
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          选择文件
        </button>
      </div>

      <!-- 已选择文件显示 -->
      <div v-else class="text-center">
        <div class="flex justify-center mb-4">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ selectedFile.name }}</h3>
        <p class="text-sm text-gray-500 mb-4">
          文件大小: {{ formatFileSize(selectedFile.size) }}
        </p>
        <div class="flex justify-center space-x-3">
          <button 
            @click.stop="resetFile"
            type="button"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            重新选择
          </button>
          <button 
            @click.stop="startAnalysis"
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            开始分析
          </button>
        </div>
      </div>

      <!-- 隐藏的文件输入 -->
      <input 
        type="file" 
        ref="fileInput" 
        class="hidden" 
        accept="application/pdf" 
        @change="handleFileSelect" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['file-selected', 'start-analysis'])

// 响应式数据
const isDragging = ref(false)
const selectedFile = ref(null)
const fileInput = ref(null)

// 文件验证
const validateFile = (file) => {
  if (!file) {
    return false
  }

  // 检查文件类型
  if (file.type !== 'application/pdf') {
    alert('请选择PDF文件')
    return false
  }

  // 检查文件大小
  if (file.size > 20 * 1024 * 1024) { // 20MB
    alert('文件大小不能超过20MB')
    return false
  }

  return true
}

// 文件选择处理
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (validateFile(file)) {
    selectedFile.value = file
    emit('file-selected', file)
  }
}

// 文件拖拽处理
const handleFileDrop = (event) => {
  event.preventDefault()
  isDragging.value = false
  
  const file = event.dataTransfer.files[0]
  if (validateFile(file)) {
    selectedFile.value = file
    emit('file-selected', file)
  }
}

// 重置文件
const resetFile = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  emit('file-selected', null)
}

// 开始分析
const startAnalysis = () => {
  if (selectedFile.value) {
    emit('start-analysis')
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
