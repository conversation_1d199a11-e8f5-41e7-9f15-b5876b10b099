<template>
  <div>
    <!-- 文件上传步骤 -->
    <FileUploadStep
      v-if="currentStep === 'upload'"
      @file-selected="handleFileSelected"
      @start-analysis="startAnalysis"
    />
    
    <!-- 分析进度步骤 -->
    <AnalysisStep
      v-if="currentStep === 'analyzing'"
      :is-analyzing="isAnalyzing"
      :progress-message="progressMessage"
      :analysis-progress="analysisProgress"
    />
    
    <!-- 分析结果步骤 -->
    <ResultStep
      v-if="currentStep === 'result'"
      :analysis-result="analysisResult"
      @reset-analysis="resetAnalysis"
      @further-analysis="onFurtherAnalysis"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import FileUploadStep from './steps/FileUploadStep.vue'
import AnalysisStep from './steps/AnalysisStep.vue'
import ResultStep from './steps/ResultStep.vue'
import { useAnalysis } from '../../composables/useAnalysis'
import { useAnalysisRouter } from '../../composables/useAnalysisRouter'

const {
  isAnalyzing,
  analysisResult,
  analysisProgress,
  progressMessage,
  handleFileSelected,
  startAnalysis,
  resetAnalysis,
  loadAnalysisResult
} = useAnalysis()

// 使用路由逻辑
useAnalysisRouter(analysisResult, loadAnalysisResult)

// 计算当前步骤
const currentStep = computed(() => {
  if (analysisResult.value) return 'result'
  if (isAnalyzing.value) return 'analyzing'
  // 如果已经有选择的文件且正在分析，显示分析步骤
  // 否则显示上传步骤
  return 'upload'
})

// 进一步分析功能
const onFurtherAnalysis = () => {
  window.alert('功能开发中')
}
</script> 