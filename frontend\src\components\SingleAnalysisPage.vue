<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />
    <MainContent>
      <AnalysisWorkflow />
    </MainContent>
    <AppFooter />
  </div>
</template>

<script setup>
import AppFooter from './layout/AppFooter.vue'
import AppHeader from './layout/AppHeader.vue'
import MainContent from './layout/MainContent.vue'
import AnalysisWorkflow from './analysis/AnalysisWorkflow.vue'
</script>

<style>
/* 可以添加额外的样式，但大部分样式已通过Tailwind类实现 */
</style>
