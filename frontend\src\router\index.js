import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../components/HomePage.vue'
import SingleAnalysisPage from '../components/SingleAnalysisPage.vue'
import BatchAnalysisPage from '../components/BatchAnalysisPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage
  },
  {
    path: '/single-analysis',
    name: 'SingleAnalysis',
    component: SingleAnalysisPage
  },
  {
    path: '/batch-analysis',
    name: 'BatchAnalysis',
    component: BatchAnalysisPage
  },
  {
    path: '/analysis/:id',
    name: 'AnalysisResult',
    component: SingleAnalysisPage,
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 