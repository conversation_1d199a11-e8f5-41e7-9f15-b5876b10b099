<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab动画演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Tab内容容器样式 */
        .tab-content-container {
            display: flex;
            width: 200%;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(0);
        }

        /* 当切换到批量分析时，向左滑动 */
        .tab-content-container.translate-batch {
            transform: translateX(-50%);
        }

        /* 每个tab内容占据50%宽度 */
        .tab-content {
            width: 50%;
            flex-shrink: 0;
        }

        /* 确保内容不会溢出 */
        .single-content,
        .batch-content {
            min-height: 400px;
        }

        /* 增强tab按钮的视觉反馈 */
        button[class*="px-6 py-3"] {
            transform: scale(1);
            transition: all 0.2s ease-in-out;
        }

        button[class*="px-6 py-3"]:hover {
            transform: scale(1.02);
        }

        button[class*="px-6 py-3"]:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">Tab切换动画演示</h1>
            
            <!-- 分析类型选择选项卡 -->
            <div class="flex mb-8 justify-center">
                <!-- 单合同分析选项卡 -->
                <button
                    id="singleTab"
                    onclick="switchTab('single')"
                    class="px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-gray-900 text-white rounded-l-lg border-r border-gray-200"
                >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"/>
                    </svg>
                    <span>单合同分析</span>
                </button>

                <!-- 批量分析选项卡 -->
                <button
                    id="batchTab"
                    onclick="switchTab('batch')"
                    class="px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-white text-gray-700 hover:bg-gray-50 rounded-r-lg"
                >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                    </svg>
                    <span>批量分析</span>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="w-full max-w-2xl mx-auto relative overflow-hidden">
                <!-- Tab内容容器 -->
                <div id="tabContainer" class="tab-content-container">
                    <!-- 单合同分析内容 -->
                    <div class="tab-content single-content bg-white rounded-2xl shadow-lg p-8">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">单合同分析</h3>
                            <p class="text-sm text-gray-500 mb-4">
                                上传单个合同文件进行详细分析
                            </p>
                            <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                                选择文件
                            </button>
                        </div>
                    </div>

                    <!-- 批量分析内容 -->
                    <div class="tab-content batch-content bg-white rounded-2xl shadow-lg p-8">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">批量合同分析</h3>
                            <p class="text-sm text-gray-500 mb-4">
                                同时上传多个合同文件进行批量分析
                            </p>
                            <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
                                选择多个文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'single';

        function switchTab(tab) {
            if (currentTab === tab) return;
            
            currentTab = tab;
            const container = document.getElementById('tabContainer');
            const singleTab = document.getElementById('singleTab');
            const batchTab = document.getElementById('batchTab');
            
            if (tab === 'batch') {
                container.classList.add('translate-batch');
                singleTab.className = 'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-white text-gray-700 hover:bg-gray-50 rounded-l-lg border-r border-gray-200';
                batchTab.className = 'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-gray-900 text-white rounded-r-lg';
            } else {
                container.classList.remove('translate-batch');
                singleTab.className = 'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-gray-900 text-white rounded-l-lg border-r border-gray-200';
                batchTab.className = 'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2 bg-white text-gray-700 hover:bg-gray-50 rounded-r-lg';
            }
        }
    </script>
</body>
</html>
